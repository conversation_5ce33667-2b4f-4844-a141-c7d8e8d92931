"""Add LINEAR_CONNECTOR to SearchSourceConnectorType enum

Revision ID: 2
Revises: e55302644c51

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2"
down_revision: Union[str, None] = "e55302644c51"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum
            WHERE enumlabel = 'LINEAR_CONNECTOR'
            AND enumtypid = (
                SELECT oid FROM pg_type WHERE typname = 'searchsourceconnectortype'
            )
        ) THEN
            ALTER TYPE searchsourceconnectortype ADD VALUE 'LINEAR_CONNECTOR';
        END IF;
    END$$;
    """
    )


#


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Downgrading removal of an enum value requires recreating the type
    op.execute(
        "ALTER TYPE searchsourceconnectortype RENAME TO searchsourceconnectortype_old"
    )
    op.execute(
        "CREATE TYPE searchsourceconnectortype AS ENUM('SERPER_API', 'TAVILY_API', 'SLACK_CONNECTOR', 'NOTION_CONNECTOR', 'GITHUB_CONNECTOR')"
    )
    op.execute(
        (
            "ALTER TABLE search_source_connectors ALTER COLUMN connector_type TYPE searchsourceconnectortype USING "
            "connector_type::text::searchsourceconnectortype"
        )
    )
    op.execute("DROP TYPE searchsourceconnectortype_old")

    pass
    # ### end Alembic commands ###
